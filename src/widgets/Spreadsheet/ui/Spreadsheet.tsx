import 'handsontable/dist/handsontable.full.min.css'
import './Spreadsheet.scss'

import Handsontable from 'handsontable'
import { CellValue } from 'handsontable/common'
import { registerLanguageDictionary, ruRU } from 'handsontable/i18n'
import { DetailedSettings } from 'handsontable/plugins/collapsibleColumns'
import { CommentObject } from 'handsontable/plugins/comments'
import { HyperFormula } from 'hyperformula'
import { getStyledComment } from 'pages/CalculationsPage/ui/StationBody/lib'
import { forwardRef, RefCallback, useCallback, useEffect, useLayoutEffect, useMemo, useRef } from 'react'
import { calcCellFromAlphabet } from 'shared/lib/alphabet'
import { InputValueKeys } from 'stores/CalculationsPageStore/VaultStore/vaultStore.types.ts'

import {
  formatGesCalculationNumericValue,
  getShortcutForEditor,
  getShortcutForGrid,
  handlePastSpreadsheetCellValue,
  SHORTCUT_GROUP_ID,
} from './lib'

type GridSettings = Handsontable.GridSettings
type ColumnSettings = Handsontable.ColumnSettings
type CellSettings = Exclude<GridSettings['cell'], undefined>[0]

class NumericEditor extends Handsontable.editors.NumericEditor {
  // Создаём именованный обработчик, чтобы его можно было впоследствии удалить
  inputHandler = () => {
    const input = this.TEXTAREA as HTMLInputElement
    const value = input.value
    // Если есть запятая, заменяем её на точку и корректируем позицию курсора.
    if (value.includes(',')) {
      const caretPos = input.value.indexOf(',') + 1
      input.value = input.value.replace(',', '.')
      input.setSelectionRange(caretPos, caretPos)
    }

    // Если значение состоит только из пробелов и точки или запятой, возвращаем пустую строку.
    if (value.trim() === '.' || value.trim() === ',') {
      input.value = ''
    }
  }

  open() {
    super.open()
    const input = this.TEXTAREA as HTMLInputElement
    input.addEventListener('input', this.inputHandler)
  }

  close() {
    super.close()
    const input = this.TEXTAREA as HTMLInputElement
    input.removeEventListener('input', this.inputHandler)
  }
}

registerLanguageDictionary(ruRU)

const localStorageIsAllCollapsedKey = 'isAllCollapsed'

const collapseColumns = (
  hot: Handsontable,
  collapsibleColumns: GridSettings['collapsibleColumns'],
  headersMap: number[],
) => {
  hot.batch(() => {
    if (collapsibleColumns && (collapsibleColumns as DetailedSettings[])?.length) {
      const collapsible: number[] = []

      ;(collapsibleColumns as DetailedSettings[]).forEach((col) => {
        hot.getPlugin('collapsibleColumns').collapseSection(col)
        collapsible.push(headersMap[col.col])
      })
      localStorage.setItem('collapsibleColumns', JSON.stringify(collapsible))
    }
  })
}

const setColumnCollapse = (
  _: number[],
  destinationCollapsedColumns: number[],
  __: boolean,
  ___: boolean,
  headersMap: number[],
) => {
  let res: number[] = []
  destinationCollapsedColumns.forEach((el) => {
    const hiddenEl = headersMap[el]
    const find = res.find((item) => item === hiddenEl)
    if (find === undefined) {
      res = [...res, hiddenEl]
    }
  })
  localStorage.setItem('collapsibleColumns', JSON.stringify(res))
}

const renderCells = (row: number, col: number, cells: CellSettings[], viewOnly: boolean, columnsLength: number) => {
  const cellPropIdx = row * columnsLength + col
  const cellProps = cells[cellPropIdx]
  const isNotEdit = String(cellProps?.className).toLowerCase().includes('disabled') || viewOnly
  if (isNotEdit) {
    return { ...cellProps, readOnly: true }
  }

  return { ...cellProps }
}

const initGrid = (
  element: HTMLDivElement,
  options: GridSettings,
  viewOnly: boolean,
  headersMap: number[],
  collapsibleColumns?: GridSettings['collapsibleColumns'],
) => {
  const cells = options.cell || []
  const columns = options.columns || []
  const hot = new Handsontable(element, {
    ...options,
    cells: (row: number, col: number) => renderCells(row, col, cells, viewOnly, columns.length),
    afterColumnCollapse: (...ars) => setColumnCollapse(...ars, headersMap),
    afterColumnExpand: (...ars) => setColumnCollapse(...ars, headersMap),
  })
  collapseColumns(hot, collapsibleColumns, headersMap)

  return hot
}

interface IInputResultProp {
  comment: CommentObject | undefined
  active: boolean
  disabled: boolean
  value: string | number | boolean | undefined
  isValid: boolean
  isCalculatedByFormula?: boolean // Флаг, указывающий, что значение ячейки рассчитано формулой
}

export interface IInputResultItemProp {
  stationColNum: number
  wMin: IInputResultProp
  wMax: IInputResultProp
  pGen: IInputResultProp
}

interface SpreadsheetProps {
  hasConsumpt?: boolean
  data: CellValue[][]
  cell: GridSettings['cell']
  columns: ColumnSettings[]
  nestedHeaders?: GridSettings['nestedHeaders']
  collapsibleColumns?: GridSettings['collapsibleColumns']
  className?: GridSettings['className']
  height?: GridSettings['height']

  afterChange?: GridSettings['afterChange']
  afterDeselect?: GridSettings['afterDeselect']
  afterGetColHeader?: GridSettings['afterGetColHeader']
  afterGetRowHeader?: GridSettings['afterGetRowHeader']
  afterSelection?: GridSettings['afterSelection']
  afterSelectionEnd?: GridSettings['afterSelectionEnd']
  beforeCancelChange?: GridSettings['beforeChange']
  beforeRenderer?: GridSettings['beforeRenderer']

  viewOnly?: boolean
  inputResultProps?: IInputResultItemProp[]

  setHot?: (hot: Handsontable | null) => void
  inputResultChange?: (value: string, key: InputValueKeys, col: number) => void
}

export const Spreadsheet = forwardRef<HTMLDivElement, SpreadsheetProps>((props, ref) => {
  const {
    hasConsumpt = false,
    data = [],
    cell = [],
    columns = [],
    nestedHeaders = [],
    collapsibleColumns = [],
    className = '',
    height = 'auto',

    afterChange,
    afterDeselect,
    afterGetColHeader,
    afterGetRowHeader,
    afterSelection,
    afterSelectionEnd,
    beforeCancelChange,
    beforeRenderer,

    viewOnly = false,
    inputResultProps,

    setHot,
    inputResultChange,
  } = props
  const refContainer = useRef<HTMLDivElement | null>(null)
  const hotRef = useRef<Handsontable | null>(null)
  const toggleAllRef = useRef<HTMLElement | null>(null)
  // Массив длинна которого равна длине массива columns, а значения равны индексам колонок,
  // где установлена кнопка для сворачивания/разворачивания
  const headersMap = useRef<number[]>([])
  const mounted = useRef(false)

  const inputResultCellIndexes: number[] = useMemo(() => {
    let currentStep = 0
    const res: number[] = []
    inputResultProps?.forEach((inputResultProp) => {
      res.push(currentStep, currentStep + 1, currentStep + 2)
      currentStep += inputResultProp.stationColNum
    })

    return res
  }, [inputResultProps])

  const calculatedRow = useMemo(() => {
    if (inputResultCellIndexes.length > 0 && data.length > 0 && inputResultProps) {
      return data[0].map((_, index) => {
        const foundIndex = inputResultCellIndexes.findIndex((el) => el === index)
        if (foundIndex !== -1) {
          const inputResultProp = inputResultProps![Math.trunc(foundIndex / 3)]
          const [, value] = Object.entries(inputResultProp)[(foundIndex % 3) + 1]
          if (typeof value !== 'number') {
            return value.active ? value.value : undefined
          }

          return undefined
        }

        return undefined
      })
    }

    return []
  }, [inputResultCellIndexes, inputResultProps, data.length])

  const summaryRow = useMemo(() => {
    if (data.length > 0) {
      return data[0].map((_, index) => {
        if (index === data[0].length - 1 && hasConsumpt) {
          return ''
        }
        let res: string[] = []
        data.forEach((_, indexRow) => {
          res = [...res, `${calcCellFromAlphabet(index + 1)}${indexRow + 1}`]
        })

        return `=ROUND((${res.join('+')})/1000,3)`
      })
    }

    return []
  }, [data, hasConsumpt])

  const spreadsheetCell: CellSettings[] = useMemo(() => {
    const summaryCells: CellSettings[] = []
    const additionalCells: CellSettings[] = []
    summaryRow.forEach((_, index) => {
      const foundIndex = inputResultCellIndexes.findIndex((el) => el === index)
      let renderer = 'disabledFooter bold'

      if (inputResultProps![Math.trunc(foundIndex / 3)]) {
        const inputResultProp = inputResultProps![Math.trunc(foundIndex / 3)]
        const [key] = Object.entries(inputResultProp!)[(foundIndex % 3) + 1]
        renderer += key === 'wMin' ? ' borderLeft' : ''
      }

      summaryCells.push({
        row: data.length,
        col: index,
        renderer,
        editor: false,
        readOnly: true,
      })
    })
    if (inputResultCellIndexes.length > 0) {
      calculatedRow.forEach((_, index) => {
        const foundIndex = inputResultCellIndexes.findIndex((el) => el === index)
        if (foundIndex !== -1) {
          // индекс в списке inputResultProps = foundIndex / 3
          const inputResultProp = inputResultProps![Math.trunc(foundIndex / 3)]
          // порядок поля в элементе списка inputResultProps = (foundIndex % 3) + 1 | +1, т.к. первый элемент это stationColNum
          const [key, value] = Object.entries(inputResultProp!)[(foundIndex % 3) + 1]
          if (typeof value !== 'number') {
            let renderer = value.active && !value.disabled ? 'numeric' : 'disabledFooter'
            renderer += key === 'wMin' ? ' borderLeft' : ''
            renderer += !value.isValid ? ' isNotValid' : ''
            if (!value.disabled && value.active) {
              renderer += key === 'pGen' ? ' plantCountedNotOptimized' : ' plantCountedOptimized'
            }
            renderer += ' distributionProhibited'

            // Стилизация ячейки Эмин
            if (key === 'wMin') {
              renderer += ' eMinCell'
              // Добавляем класс для ячеек, вычисляемых по формуле
              if (value.isCalculatedByFormula) {
                renderer += ' calculatedByFormula'
              }
            }

            additionalCells.push({
              row: data.length + 1,
              col: index,
              renderer,
              editor: value.active && !value.disabled ? 'numeric' : false,
              readOnly: !value.active,
              comment: typeof value.comment === 'string' ? getStyledComment(value.comment) : value?.comment,
            })
          } else {
            additionalCells.push({
              row: data.length + 1,
              col: index,
              renderer: 'disabledFooter',
              editor: false,
              readOnly: true,
            })
          }
        } else {
          additionalCells.push({
            row: data.length + 1,
            col: index,
            renderer: 'disabledFooter',
            editor: false,
            readOnly: true,
          })
        }
      })
    }

    return [...cell, ...summaryCells, ...additionalCells].map((el) => {
      if (el.editor || typeof el.readOnly === 'boolean') {
        return {
          row: el.row,
          col: el.col,
          className: el.renderer,
          editor: el.editor === 'numeric' ? NumericEditor : el.editor,
          readOnly: el.readOnly,
          comment: el.comment,
        } as CellSettings
      }

      return {
        row: el.row,
        col: el.col,
        className: el.renderer,
        comment: el.comment,
      } as CellSettings
    })
  }, [cell, summaryRow, calculatedRow, data.length, inputResultCellIndexes, inputResultProps, viewOnly])

  const spreadsheetRowHeaders = useMemo(() => [...data.map((_, index) => String(index + 1)), 'Σ', 'Э'], [data.length])

  const spreadsheetData = useMemo(() => [...data, summaryRow, calculatedRow], [data, summaryRow, calculatedRow])

  const handleAfterGetColHeader: GridSettings['afterGetColHeader'] = (col, th, level) => {
    if (col === -1 && level === 0 && (collapsibleColumns as DetailedSettings[]).length) {
      const isAllCollapsed = localStorage.getItem(localStorageIsAllCollapsedKey)
      th.innerHTML = `<div class="relative">
                        <span class="colHeader">&nbsp</span>
                        <div class="collapsibleIndicator" style='left: calc(50% - 6px) !important;'>
                          ${isAllCollapsed === 'true' ? '+' : '-'}
                        </div>
                      </div>`
      toggleAllRef.current = th

      return
    }
    afterGetColHeader && afterGetColHeader(col, th, level)
  }

  /**
   * Блокировка копирования формул путем перетягивания (source=autofill).
   * @param changes - список ячеек с измененными данными
   * @param source - событие, которое инициировало изменение
   */
  const handleBeforeChange: GridSettings['beforeChange'] = (changes, source) => {
    const cancelChange = changes.some((cellChange) => {
      if (cellChange === null) return true
      const [, , , newValue] = cellChange

      return String(newValue).includes('=')
    })
    beforeCancelChange && beforeCancelChange(changes, source)

    return !cancelChange
  }

  const handleBeforePaste: Handsontable.GridSettings['beforePaste'] = (data, coords) => {
    if (coords && coords.length > 0) {
      const processedData = data
        .map((row) =>
          row
            .map((cellValue) => handlePastSpreadsheetCellValue(cellValue, formatGesCalculationNumericValue))
            .filter((cellValue) => cellValue !== undefined),
        )
        .filter((row) => row.length > 0)

      if (processedData.length) {
        // Очищаем оригинальный массив и вставляем обработанные данные
        data.length = 0
        data.push(...processedData)
      } else {
        return false
      }
    }
  }

  const handleAfterChange: GridSettings['afterChange'] = (changes, source) => {
    const availableChanges =
      changes?.filter(([row, col, prevValue, nextValue]) => {
        const cell = hotRef.current?.getCellMeta(row, Number(col))

        return !String(cell?.className).toLowerCase().includes('disabled') && prevValue !== nextValue
      }) ?? null
    const countedChanges = availableChanges?.filter((availableChange) => availableChange[0] === 25)
    if (countedChanges !== null && countedChanges !== undefined && countedChanges.length > 0) {
      countedChanges.forEach((countedChange) => {
        const [row, coll, _, value] = countedChange
        const foundIndex = inputResultCellIndexes.findIndex((el) => el === coll)
        if (foundIndex !== -1) {
          const inputResultIdx = foundIndex % 3
          if (row === 25) {
            switch (inputResultIdx) {
              case 0:
                inputResultChange && inputResultChange(value || '', 'W_MIN', coll as number)
                break
              case 1:
                inputResultChange && inputResultChange(value || '', 'P_GEN_TARGET', coll as number)
                break
              case 2:
                inputResultChange && inputResultChange(value || '', 'W_MAX', coll as number)
                break
            }
          }
        }
      })
    }
    if (afterChange && availableChanges?.length) {
      afterChange(availableChanges, source)
    }
  }

  const toggleAllHeaders = useCallback(() => {
    const collapsibleColumns = hotRef.current?.getPlugin('collapsibleColumns')
    const isAllCollapsed = localStorage.getItem(localStorageIsAllCollapsedKey)

    if (isAllCollapsed === 'true') {
      localStorage.setItem(localStorageIsAllCollapsedKey, 'false')
      collapsibleColumns?.expandAll()
    } else {
      localStorage.setItem(localStorageIsAllCollapsedKey, 'true')
      collapsibleColumns?.collapseAll()
    }
  }, [])

  const memoizedInputListener = useCallback((e: Event) => {
    const target = e.target as HTMLInputElement
    target.value = formatGesCalculationNumericValue(target.value)
  }, [])

  /**
   * Инициализация таблицы
   * @param divRef - Ссылка на элемент, в который будем монтироваться таблица Handsontable
   */
  const initHandsontable = (divRef: HTMLDivElement | null) => {
    if (divRef && nestedHeaders.length > 0) {
      const optionsGrid: GridSettings = {
        className: 'spreadsheet ' + className,
        language: ruRU.languageCode,
        licenseKey: 'non-commercial-and-evaluation',
        formulas: {
          engine: HyperFormula,
        },
        // Стили отображения таблицы
        height,
        width: 'auto',
        colWidths: 60,
        autoRowSize: true,
        comments: true,
        // Данные для отображения таблицы
        selectionMode: 'multiple',
        viewportColumnRenderingOffset: 40,
        columns,
        nestedHeaders,
        collapsibleColumns:
          (collapsibleColumns as DetailedSettings[]).length > 0 && nestedHeaders.length > 0
            ? collapsibleColumns
            : false,
        rowHeaders: spreadsheetRowHeaders,
        maxRows: spreadsheetData.length,
        // Обработчики для вводимых данных
        afterChange: handleAfterChange,
        beforeChange: handleBeforeChange,
        // Обработчики фокуса ячейки
        afterSelection,
        afterSelectionEnd,
        afterDeselect,
        // Обработчики отрисовки шапок
        afterGetColHeader: handleAfterGetColHeader,
        afterGetRowHeader,
        // Обработчик отрисовки данных таблицы
        beforeRenderer,
        // Обработчик данных для события вставки
        beforePaste: handleBeforePaste,
      }
      const hot = initGrid(divRef, optionsGrid, viewOnly, headersMap.current, collapsibleColumns)
      setHot && setHot(hot)
      hotRef.current = hot

      const gridContext = hotRef.current.getShortcutManager().getContext('grid')
      const editorContext = hotRef.current.getShortcutManager().getContext('editor')

      gridContext?.addShortcut(getShortcutForGrid(hotRef.current))
      editorContext?.addShortcut(getShortcutForEditor(hotRef.current))

      if (toggleAllRef.current) {
        toggleAllRef.current?.addEventListener('click', toggleAllHeaders)
      }
    }
    mounted.current = true
  }

  const setRef: RefCallback<HTMLDivElement> = (divRef) => {
    refContainer.current = divRef
    !mounted.current && initHandsontable(divRef)
    if (typeof ref === 'function') {
      ref(divRef)
    } else if (ref?.current) {
      ref.current = divRef
    }
  }

  /**
   * Обновление данных таблицы с дебаунсом
   */
  const updateDataTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useLayoutEffect(() => {
    if (mounted.current && hotRef.current) {
      // Очищаем предыдущий таймер
      if (updateDataTimeoutRef.current) {
        clearTimeout(updateDataTimeoutRef.current)
      }

      // Устанавливаем новый таймер с дебаунсом
      updateDataTimeoutRef.current = setTimeout(() => {
        console.time('updateData')
        hotRef.current?.updateData(spreadsheetData)
        console.timeEnd('updateData')
      }, 10) // 10ms дебаунс
    }

    return () => {
      if (updateDataTimeoutRef.current) {
        clearTimeout(updateDataTimeoutRef.current)
      }
    }
  }, [spreadsheetData])

  const optionsGrid: GridSettings = useMemo(
    () => ({
      cell: spreadsheetCell,
      cells: (row: number, col: number) => renderCells(row, col, spreadsheetCell, viewOnly, columns.length),
      afterChange: handleAfterChange,
      afterGetColHeader: handleAfterGetColHeader,
      afterSelectionEnd,
      afterSelection,
    }),
    [
      spreadsheetCell,
      viewOnly,
      columns.length,
      handleAfterChange,
      handleAfterGetColHeader,
      afterSelectionEnd,
      afterSelection,
    ],
  )

  /**
   * Обновление настроек таблицы с дебаунсом
   */
  const updateSettingsTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useLayoutEffect(() => {
    if (mounted.current && refContainer?.current && nestedHeaders.length > 0) {
      // Очищаем предыдущий таймер
      if (updateSettingsTimeoutRef.current) {
        clearTimeout(updateSettingsTimeoutRef.current)
      }

      // Устанавливаем новый таймер с дебаунсом
      updateSettingsTimeoutRef.current = setTimeout(() => {
        console.time('updateSettings')
        hotRef.current?.updateSettings(optionsGrid)
        console.timeEnd('updateSettings')
      }, 10) // 10ms дебаунс
    }

    return () => {
      if (updateSettingsTimeoutRef.current) {
        clearTimeout(updateSettingsTimeoutRef.current)
      }
    }
  }, [nestedHeaders, collapsibleColumns, optionsGrid])

  useEffect(() => {
    if (nestedHeaders.length > 0) {
      let cursor: number = 0
      nestedHeaders[0]?.forEach((el, index: number) => {
        if (typeof el !== 'string') {
          const colspan = el.colspan
          for (let i = 0; i < colspan; i++) {
            headersMap.current[cursor] = index
            cursor = cursor + 1
          }
        }
      })
    }
  }, [nestedHeaders])

  useEffect(() => {
    refContainer.current?.addEventListener('input', memoizedInputListener)

    return () => {
      refContainer.current?.removeEventListener('input', memoizedInputListener)
    }
  }, [memoizedInputListener])

  useLayoutEffect(() => {
    // сохраняем в localStorage статус, что таблица свернута по умолчанию
    localStorage.setItem(localStorageIsAllCollapsedKey, 'true')

    return () => {
      localStorage.removeItem(localStorageIsAllCollapsedKey)
      refContainer.current = null
      setHot && setHot(null)
      if (toggleAllRef.current) {
        toggleAllRef.current?.removeEventListener('click', toggleAllHeaders)
      }
      if (hotRef.current) {
        const gridContext = hotRef.current.getShortcutManager().getContext('grid')
        const editorContext = hotRef.current.getShortcutManager().getContext('editor')
        gridContext?.removeShortcutsByGroup(SHORTCUT_GROUP_ID)
        editorContext?.removeShortcutsByGroup(SHORTCUT_GROUP_ID)
        hotRef.current = null
      }
    }
  }, [])

  return <div ref={setRef} />
})
