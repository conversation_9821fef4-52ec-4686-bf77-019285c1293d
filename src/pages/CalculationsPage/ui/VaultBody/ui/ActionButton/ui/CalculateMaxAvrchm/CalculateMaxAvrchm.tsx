import { MessagesWarnings } from 'entities/widgets/Vault.entities.ts'
import { CalculationActionButton } from 'pages/CalculationsPage/ui/CalculationButton/CalculationActionButton.tsx'
import { useStore } from 'stores/useStore.ts'

export const CalculateMaxAvrchm = () => {
  const { calculationsPageStore } = useStore()
  const { vaultStore, isLastDay, viewOnly, editMode, plantsListForAside } = calculationsPageStore
  const { isEditRows } = vaultStore

  return (
    <CalculationActionButton
      buttonName={MessagesWarnings.CALCULATE_MAX_AVRCHM}
      viewOnly={viewOnly}
      isEditRows={isEditRows}
      isLastDay={isLastDay}
      editMode={editMode}
      onClick={async () => {
        await vaultStore.actionsVault(MessagesWarnings.CALCULATE_MAX_AVRCHM, plantsListForAside)
      }}
    />
  )
}
